#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文字体支持
"""

import os
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import platform

def test_chinese_font():
    """测试中文字体是否可用"""
    print("测试中文字体支持...")
    
    # 获取系统信息
    system = platform.system()
    print(f"操作系统: {system}")
    
    # 尝试注册中文字体
    chinese_font = None
    
    if system == "Windows":
        font_paths = [
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
        ]
    elif system == "Darwin":  # macOS
        font_paths = [
            "/System/Library/Fonts/PingFang.ttc",
            "/Library/Fonts/Arial Unicode MS.ttf",
        ]
    else:  # Linux
        font_paths = [
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
        ]
    
    print("\n检查字体文件:")
    for font_path in font_paths:
        exists = os.path.exists(font_path)
        print(f"  {font_path}: {'存在' if exists else '不存在'}")
        
        if exists and not chinese_font:
            try:
                pdfmetrics.registerFont(TTFont('TestChineseFont', font_path))
                chinese_font = 'TestChineseFont'
                print(f"  成功注册字体: {font_path}")
                break
            except Exception as e:
                print(f"  注册字体失败: {e}")
    
    if not chinese_font:
        chinese_font = 'Helvetica'
        print("使用默认字体: Helvetica")
    
    # 创建测试PDF
    print(f"\n使用字体: {chinese_font}")
    print("创建测试PDF...")
    
    c = canvas.Canvas("test_chinese.pdf", pagesize=(600, 400))
    
    # 设置字体和颜色
    c.setFillColorRGB(0.4, 0.4, 0.4)  # 深灰色
    c.setFont(chinese_font, 60)
    
    # 测试文字
    test_texts = [
        "中文测试",
        "汉字显示测试",
        "PDF水印工具",
        "根据金管局《银行保险机构数据安全管理办法》"
    ]
    
    y_position = 300
    for text in test_texts:
        try:
            text_width = c.stringWidth(text, chinese_font, 60)
            x_position = (600 - text_width) / 2  # 居中
            c.drawString(x_position, y_position, text)
            y_position -= 70
            print(f"  成功绘制: {text}")
        except Exception as e:
            print(f"  绘制失败 '{text}': {e}")
    
    c.save()
    print("\n测试PDF已保存为: test_chinese.pdf")
    
    return chinese_font

if __name__ == "__main__":
    test_chinese_font()
