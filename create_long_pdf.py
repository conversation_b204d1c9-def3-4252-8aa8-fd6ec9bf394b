#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建一个长页面的测试PDF
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

def create_long_pdf():
    """创建一个长页面的测试PDF"""
    
    # 注册中文字体
    try:
        pdfmetrics.registerFont(TTFont('ChineseFont', 'C:/Windows/Fonts/simsun.ttc'))
        font_name = 'ChineseFont'
    except:
        font_name = 'Helvetica'
    
    # 创建一个长页面 (宽度正常，高度是标准的3倍)
    width, height = letter
    long_height = height * 3  # 3倍高度
    
    c = canvas.Canvas("long_test.pdf", pagesize=(width, long_height))
    
    # 设置字体
    c.setFont(font_name, 12)
    c.setFillColorRGB(0, 0, 0)  # 黑色文字
    
    # 添加一些测试内容
    y_position = long_height - 50
    
    content = [
        "这是一个长页面测试PDF",
        "",
        "根据金管局《银行保险机构数据安全管理办法》（金规（2024）",
        "24号）、总公司技术部2025年信息安全和数据安全专项检查问题、总",
        "公司技术部信息安全相关制度、总公司'关于进一步完善集团业务连'",
        "续性管理工作机制的通知、《中华人民共和国反洗钱法》等要求，",
        "结合公司实际，质量保障部修订了10份安全类制度，具体如下：",
        "",
        "第一部分内容...",
        "这里是第一部分的详细内容，用于测试水印在长页面上的显示效果。",
        "内容应该足够长，以便测试多个水印的分布情况。",
        "",
        "第二部分内容...",
        "这里是第二部分的详细内容。",
        "继续添加更多内容以填充页面。",
        "",
        "第三部分内容...",
        "这里是第三部分的详细内容。",
        "页面应该足够长以需要多个水印。",
        "",
        "第四部分内容...",
        "这里是第四部分的详细内容。",
        "测试水印工具的多水印功能。",
    ]
    
    # 重复内容以填充长页面
    for _ in range(3):  # 重复3次内容
        for line in content:
            if y_position < 50:  # 如果接近页面底部，停止
                break
            c.drawString(50, y_position, line)
            y_position -= 20
    
    # 在页面的不同位置添加标记，帮助识别水印位置
    c.setFillColorRGB(0.8, 0.8, 0.8)  # 浅灰色
    c.setFont(font_name, 8)
    
    # 添加高度标记
    for i in range(0, int(long_height), 200):
        c.drawString(width - 100, i + 10, f"高度: {i}px")
    
    c.save()
    print(f"长页面测试PDF已创建: long_test.pdf")
    print(f"页面尺寸: {width} x {long_height}")

if __name__ == "__main__":
    create_long_pdf()
