#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试用的PDF文件
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

def create_test_pdf():
    """创建一个测试用的PDF文件"""
    filename = "test_document.pdf"
    
    # 创建PDF
    c = canvas.Canvas(filename, pagesize=A4)
    width, height = A4
    
    # 尝试注册中文字体（如果可用）
    try:
        # Windows系统的中文字体
        font_paths = [
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Chinese', font_path))
                chinese_font = 'Chinese'
                break
        else:
            chinese_font = 'Helvetica'
    except:
        chinese_font = 'Helvetica'
    
    # 第一页
    c.setFont(chinese_font, 16)
    c.drawString(2*cm, height - 3*cm, "测试文档 - 第一页")
    
    c.setFont("Helvetica", 12)
    c.drawString(2*cm, height - 5*cm, "This is a test document for watermark testing.")
    c.drawString(2*cm, height - 6*cm, "You can use this PDF to test the watermark tool.")
    
    # 添加一些示例内容
    content_lines = [
        "附件1: 银联数据数据安全管理办法",
        "附件2: 银联数据信息资产分级管理办法", 
        "附件3: 银联数据信息安全策略",
        "附件4: 银联数据反洗钱工作管理办法",
        "附件5: 银联数据互联网系统信息安全管理细则",
        "附件6: 银联数据数据安全实施细则",
        "附件7: 银联数据生产系统敏感信息脱敏规则",
        "附件8: 银联数据信息泄露事件应急处理预案",
        "附件9: 银联数据生产安全事件对外沟通应急预案及",
        "附件10: 银联数据应用安全评估与漏洞管理细则"
    ]
    
    y_position = height - 8*cm
    for line in content_lines:
        if chinese_font == 'Chinese':
            c.setFont(chinese_font, 12)
        else:
            c.setFont("Helvetica", 12)
        c.drawString(2*cm, y_position, line)
        y_position -= 0.8*cm
    
    c.showPage()
    
    # 第二页
    c.setFont(chinese_font, 16)
    c.drawString(2*cm, height - 3*cm, "测试文档 - 第二页")
    
    c.setFont("Helvetica", 12)
    c.drawString(2*cm, height - 5*cm, "Second page content for testing watermark on multiple pages.")
    
    # 添加更多内容
    y_position = height - 7*cm
    for i in range(15):
        c.drawString(2*cm, y_position, f"Line {i+1}: This is sample content for watermark testing.")
        y_position -= 0.6*cm
        if y_position < 2*cm:
            break
    
    c.showPage()
    
    # 第三页
    c.setFont(chinese_font, 16)
    c.drawString(2*cm, height - 3*cm, "测试文档 - 第三页")
    
    c.setFont("Helvetica", 12)
    c.drawString(2*cm, height - 5*cm, "Third page with different content layout.")
    
    # 添加表格样式的内容
    y_position = height - 7*cm
    headers = ["序号", "项目名称", "状态", "备注"]
    
    # 表头
    x_positions = [2*cm, 4*cm, 8*cm, 12*cm]
    for i, header in enumerate(headers):
        c.drawString(x_positions[i], y_position, header)
    
    y_position -= 0.8*cm
    
    # 表格内容
    for i in range(10):
        c.drawString(x_positions[0], y_position, str(i+1))
        c.drawString(x_positions[1], y_position, f"项目{i+1}")
        c.drawString(x_positions[2], y_position, "进行中" if i % 2 == 0 else "已完成")
        c.drawString(x_positions[3], y_position, f"备注信息{i+1}")
        y_position -= 0.6*cm
        if y_position < 2*cm:
            break
    
    c.save()
    print(f"测试PDF文件已创建: {filename}")
    return filename

if __name__ == "__main__":
    create_test_pdf()
