#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多水印功能
"""

import os
import sys
sys.path.append('.')

from pdf_watermark_tool import PDFWatermarkTool
from pypdf import PdfReader, PdfWriter
from io import BytesIO

def test_multi_watermark():
    """测试多水印功能"""
    
    # 创建水印工具实例（不启动GUI）
    import tkinter as tk
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    tool = PDFWatermarkTool(root)
    
    # 设置测试参数
    tool.watermark_text.set("测试水印\n多行显示")
    tool.font_size.set(60)
    tool.opacity.set(0.4)
    tool.rotation.set(45)
    
    print("测试多水印功能...")
    print(f"水印文字: {tool.watermark_text.get()}")
    print(f"字体大小: {tool.font_size.get()}")
    print(f"使用字体: {tool.chinese_font}")
    
    # 测试不同高度的页面
    test_cases = [
        ("标准页面", 612, 792),      # 标准Letter尺寸
        ("长页面", 612, 1584),       # 2倍高度
        ("超长页面", 612, 2376),     # 3倍高度
    ]
    
    for name, width, height in test_cases:
        print(f"\n测试 {name} ({width}x{height}):")
        
        # 创建水印
        watermark_buffer = tool.create_watermark_pdf(width, height)
        
        # 计算预期的水印数量
        line_height = tool.font_size.get() * 1.2
        total_text_height = 2 * line_height  # 2行文字
        watermark_spacing = max(height / 3, total_text_height * 2)
        expected_watermarks = max(1, int(height / watermark_spacing) + 1)
        
        print(f"  页面高度: {height}")
        print(f"  水印间距: {watermark_spacing:.1f}")
        print(f"  预期水印数量: {expected_watermarks}")
        
        # 保存测试水印PDF
        test_filename = f"test_watermark_{name.replace(' ', '_')}.pdf"
        with open(test_filename, 'wb') as f:
            f.write(watermark_buffer.getvalue())
        print(f"  测试水印已保存: {test_filename}")
    
    # 测试实际的长页面PDF
    if os.path.exists("long_test.pdf"):
        print(f"\n测试实际长页面PDF:")
        
        try:
            # 读取长页面PDF
            with open("long_test.pdf", "rb") as f:
                reader = PdfReader(f)
                writer = PdfWriter()
                
                for page_num, page in enumerate(reader.pages):
                    print(f"  处理第 {page_num + 1} 页...")
                    
                    # 获取页面尺寸
                    page_width = float(page.mediabox.width)
                    page_height = float(page.mediabox.height)
                    print(f"    页面尺寸: {page_width} x {page_height}")
                    
                    # 创建水印
                    watermark_buffer = tool.create_watermark_pdf(page_width, page_height)
                    watermark_reader = PdfReader(watermark_buffer)
                    watermark_page = watermark_reader.pages[0]
                    
                    # 合并水印
                    page.merge_page(watermark_page)
                    writer.add_page(page)
                
                # 保存结果
                output_filename = "long_test_with_watermark.pdf"
                with open(output_filename, "wb") as output_file:
                    writer.write(output_file)
                
                print(f"  带水印的长页面PDF已保存: {output_filename}")
                
        except Exception as e:
            print(f"  处理长页面PDF时出错: {e}")
    
    root.destroy()
    print("\n测试完成！")

if __name__ == "__main__":
    test_multi_watermark()
