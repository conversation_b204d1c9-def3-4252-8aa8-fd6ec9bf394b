#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

def create_test_pdf():
    """创建一个测试PDF"""
    filename = "test_skip_first.pdf"
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # 添加一些内容
    c.drawString(100, height - 100, "这是一个测试PDF")
    c.drawString(100, height - 150, "用来测试跳过第一个水印的功能")
    c.drawString(100, height - 200, "页面高度: {}".format(height))
    
    c.save()
    print(f"测试PDF已创建: {filename}")
    return filename

def test_watermark_positions():
    """测试水印位置计算 - 直接复制核心逻辑"""
    from pypdf import PdfReader, PdfWriter
    from reportlab.pdfgen import canvas
    from reportlab.lib.colors import Color
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from io import BytesIO
    import math

    # 创建测试PDF
    test_file = create_test_pdf()

    # 水印参数
    watermark_text = "测试水印"
    opacity = 0.3
    font_size = 48
    rotation = 45

    # 添加水印
    output_file = "test_skip_first_watermarked.pdf"

    try:
        # 注册字体
        font_path = "C:/Windows/Fonts/simhei.ttf"
        if os.path.exists(font_path):
            pdfmetrics.registerFont(TTFont('SimHei', font_path))
            font_name = 'SimHei'
        else:
            font_name = 'Helvetica'

        # 读取输入PDF
        reader = PdfReader(test_file)
        writer = PdfWriter()

        for page_num, page in enumerate(reader.pages):
            # 获取页面尺寸
            page_width = float(page.mediabox.width)
            page_height = float(page.mediabox.height)

            print(f"页面 {page_num + 1}: {page_width:.0f} x {page_height:.0f}")

            # 创建水印PDF
            buffer = BytesIO()
            c = canvas.Canvas(buffer, pagesize=(page_width, page_height))

            # 设置透明度
            c.setFillColor(Color(0, 0, 0, alpha=opacity))
            c.setFont(font_name, font_size)

            # 计算文字尺寸
            text_width = c.stringWidth(watermark_text, font_name, font_size)
            text_height = font_size
            total_text_height = text_height * 1.2

            # 计算水印位置 - 使用修改后的逻辑
            watermark_spacing = max(page_height / 3, total_text_height * 2)
            num_watermarks = max(1, int(page_height / watermark_spacing) + 1)

            if num_watermarks == 1:
                y_positions = [page_height / 2]
            else:
                margin = total_text_height
                available_height = page_height - 2 * margin
                if num_watermarks > 1:
                    spacing = available_height / (num_watermarks - 1)
                    y_positions = [margin + i * spacing for i in range(num_watermarks)]
                else:
                    y_positions = [page_height / 2]

            # 跳过第一个水印位置
            if len(y_positions) > 1:
                y_positions = y_positions[1:]
                print(f"  跳过第一个水印后，剩余位置: {len(y_positions)} 个")
            else:
                print(f"  只有一个水印，保留: {len(y_positions)} 个")

            # 绘制水印
            for y in y_positions:
                c.saveState()
                c.translate(page_width / 2, y)
                c.rotate(rotation)
                c.drawCentredString(0, 0, watermark_text)
                c.restoreState()

            c.save()
            buffer.seek(0)

            # 合并水印
            watermark_reader = PdfReader(buffer)
            watermark_page = watermark_reader.pages[0]
            page.merge_page(watermark_page)
            writer.add_page(page)

        # 保存结果
        with open(output_file, 'wb') as output:
            writer.write(output)

        print(f"带水印的PDF已保存: {output_file}")

        # 检查文件是否存在
        if os.path.exists(output_file):
            print("✓ 水印添加成功")
        else:
            print("✗ 水印添加失败")

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_watermark_positions()
