#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF水印添加工具
支持导入PDF文件并在每页添加自定义水印
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from pathlib import Path
import threading
from io import BytesIO

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from pypdf import PdfReader, PdfWriter
    LIBRARIES_AVAILABLE = True
except ImportError:
    try:
        # 尝试使用PyPDF2作为备选
        from PyPDF2 import PdfReader, PdfWriter
        from reportlab.pdfgen import canvas
        LIBRARIES_AVAILABLE = True
    except ImportError:
        LIBRARIES_AVAILABLE = False


class PDFWatermarkTool:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF水印添加工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 变量
        self.input_file = tk.StringVar()
        self.output_file = tk.StringVar()
        self.watermark_text = tk.StringVar(value="示例水印文字")
        self.font_size = tk.IntVar(value=60)  # 增大字体大小
        self.opacity = tk.DoubleVar(value=0.3)
        self.rotation = tk.IntVar(value=45)

        # 注册中文字体
        self.setup_chinese_font()

        self.setup_ui()

    def setup_chinese_font(self):
        """设置中文字体支持"""
        try:
            # 尝试注册常见的中文字体
            import platform
            system = platform.system()

            if system == "Windows":
                # Windows系统字体路径
                font_paths = [
                    "C:/Windows/Fonts/simsun.ttc",  # 宋体
                    "C:/Windows/Fonts/simhei.ttf",  # 黑体
                    "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
                ]
            elif system == "Darwin":  # macOS
                font_paths = [
                    "/System/Library/Fonts/PingFang.ttc",
                    "/Library/Fonts/Arial Unicode MS.ttf",
                ]
            else:  # Linux
                font_paths = [
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                ]

            self.chinese_font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                        self.chinese_font = 'ChineseFont'
                        break
                    except:
                        continue

            # 如果没有找到中文字体，使用默认字体
            if not self.chinese_font:
                self.chinese_font = 'Helvetica'

        except Exception as e:
            print(f"字体设置警告: {e}")
            self.chinese_font = 'Helvetica'
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 输入文件
        ttk.Label(file_frame, text="输入PDF:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Entry(file_frame, textvariable=self.input_file, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=(0, 5))
        ttk.Button(file_frame, text="浏览", command=self.browse_input_file).grid(row=0, column=2, pady=(0, 5))
        
        # 输出文件
        ttk.Label(file_frame, text="输出PDF:").grid(row=1, column=0, sticky=tk.W)
        ttk.Entry(file_frame, textvariable=self.output_file, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 5))
        ttk.Button(file_frame, text="浏览", command=self.browse_output_file).grid(row=1, column=2)
        
        # 水印设置区域
        watermark_frame = ttk.LabelFrame(main_frame, text="水印设置", padding="10")
        watermark_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        watermark_frame.columnconfigure(1, weight=1)
        
        # 水印文字
        ttk.Label(watermark_frame, text="水印文字:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        text_entry = tk.Text(watermark_frame, height=3, width=40)
        text_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(0, 5))
        text_entry.insert("1.0", self.watermark_text.get())
        text_entry.bind('<KeyRelease>', lambda e: self.watermark_text.set(text_entry.get("1.0", tk.END).strip()))
        
        # 字体大小
        ttk.Label(watermark_frame, text="字体大小:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        size_frame = ttk.Frame(watermark_frame)
        size_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(0, 5))
        ttk.Scale(size_frame, from_=30, to=120, variable=self.font_size, orient=tk.HORIZONTAL).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(size_frame, textvariable=self.font_size).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 透明度
        ttk.Label(watermark_frame, text="透明度:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        opacity_frame = ttk.Frame(watermark_frame)
        opacity_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(0, 5))
        ttk.Scale(opacity_frame, from_=0.1, to=1.0, variable=self.opacity, orient=tk.HORIZONTAL).pack(side=tk.LEFT, fill=tk.X, expand=True)
        opacity_label = ttk.Label(opacity_frame, text="")
        opacity_label.pack(side=tk.RIGHT, padx=(5, 0))
        
        def update_opacity_label(*args):
            opacity_label.config(text=f"{self.opacity.get():.1f}")
        self.opacity.trace('w', update_opacity_label)
        update_opacity_label()
        
        # 旋转角度
        ttk.Label(watermark_frame, text="旋转角度:").grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        rotation_frame = ttk.Frame(watermark_frame)
        rotation_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(5, 0), pady=(0, 5))
        ttk.Scale(rotation_frame, from_=-90, to=90, variable=self.rotation, orient=tk.HORIZONTAL).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Label(rotation_frame, textvariable=self.rotation).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="预览水印", command=self.preview_watermark).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="添加水印", command=self.add_watermark).pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="就绪")
        self.status_label.grid(row=4, column=0, columnspan=2, pady=(5, 0))
        
    def browse_input_file(self):
        """浏览输入文件"""
        filename = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.input_file.set(filename)
            # 自动设置输出文件名
            if not self.output_file.get():
                input_path = Path(filename)
                output_path = input_path.parent / f"{input_path.stem}_watermarked{input_path.suffix}"
                self.output_file.set(str(output_path))
    
    def browse_output_file(self):
        """浏览输出文件"""
        filename = filedialog.asksaveasfilename(
            title="保存PDF文件",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.output_file.set(filename)
    
    def create_watermark_pdf(self, width, height):
        """创建水印PDF"""
        buffer = BytesIO()
        c = canvas.Canvas(buffer, pagesize=(width, height))

        # 设置透明度
        c.setFillAlpha(self.opacity.get())

        # 设置字体和颜色 - 使用更深更粗的颜色
        c.setFillColorRGB(0.2, 0.2, 0.2)  # 更深的灰色，提高可见性和对比度
        c.setFont(self.chinese_font, self.font_size.get())

        # 设置描边以增加字体粗细效果
        c.setStrokeColorRGB(0.3, 0.3, 0.3)  # 描边颜色
        c.setLineWidth(0.5)  # 描边宽度

        # 获取水印文字
        watermark_lines = self.watermark_text.get().split('\n')

        # 计算水印位置和间距
        line_height = self.font_size.get() * 1.2
        total_text_height = len(watermark_lines) * line_height

        # 根据页面高度计算需要多少个水印
        # 水印间距设为页面高度的1/3，确保长页面有足够的水印覆盖
        watermark_spacing = max(height / 3, total_text_height * 2)  # 最小间距为文字高度的2倍
        total_watermarks = max(1, int(height / watermark_spacing) + 1)

        # 计算水印的垂直位置 - 跳过第一个位置，从第二个开始
        margin = total_text_height * 1.5  # 增加上边距，避免第一个水印被截断
        available_height = height - margin - total_text_height  # 下边距

        if total_watermarks == 1:
            # 单个水印时，放在页面中下部，避免顶部
            y_positions = [height * 0.4]
        elif total_watermarks == 2:
            # 两个水印时，只放第二个（跳过第一个）
            y_positions = [height * 0.3]
        else:
            # 多个水印时，从第二个位置开始分布
            spacing = available_height / (total_watermarks - 1)
            # 跳过第一个位置，从第二个开始
            y_positions = [margin + i * spacing for i in range(1, total_watermarks)]

        center_x = width / 2

        # 为每个位置绘制水印
        for y_pos in y_positions:
            c.saveState()
            c.translate(center_x, y_pos)
            c.rotate(self.rotation.get())

            # 绘制多行文字，居中对齐，使用双重绘制增加粗细效果
            for i, line in enumerate(watermark_lines):
                if line.strip():
                    # 计算文字宽度以实现居中
                    text_width = c.stringWidth(line.strip(), self.chinese_font, self.font_size.get())
                    x_offset = -text_width / 2
                    y_offset = (len(watermark_lines) - 1) * line_height / 2 - i * line_height

                    # 先绘制稍微偏移的文字作为"阴影"增加粗细效果
                    c.setFillColorRGB(0.3, 0.3, 0.3)  # 稍浅的颜色作为阴影
                    c.drawString(x_offset + 0.3, y_offset - 0.3, line.strip())

                    # 再绘制主文字
                    c.setFillColorRGB(0.2, 0.2, 0.2)  # 主文字颜色
                    c.drawString(x_offset, y_offset, line.strip())

            c.restoreState()

        c.save()
        buffer.seek(0)
        return buffer
    
    def preview_watermark(self):
        """预览水印效果"""
        if not self.input_file.get():
            messagebox.showerror("错误", "请先选择输入PDF文件")
            return
        
        try:
            # 读取第一页来预览
            reader = PdfReader(self.input_file.get())
            first_page = reader.pages[0]
            
            # 获取页面尺寸
            page_width = float(first_page.mediabox.width)
            page_height = float(first_page.mediabox.height)
            
            messagebox.showinfo("预览信息", 
                f"水印预览:\n"
                f"文字: {self.watermark_text.get()[:50]}{'...' if len(self.watermark_text.get()) > 50 else ''}\n"
                f"字体大小: {self.font_size.get()}\n"
                f"透明度: {self.opacity.get():.1f}\n"
                f"旋转角度: {self.rotation.get()}°\n"
                f"页面尺寸: {page_width:.0f} x {page_height:.0f}")
        except Exception as e:
            messagebox.showerror("错误", f"预览失败: {str(e)}")
    
    def add_watermark_worker(self):
        """添加水印的工作线程"""
        try:
            self.status_label.config(text="正在处理...")
            self.progress.start()
            
            # 读取输入PDF
            reader = PdfReader(self.input_file.get())
            writer = PdfWriter()
            
            total_pages = len(reader.pages)
            
            for page_num, page in enumerate(reader.pages):
                # 获取页面尺寸
                page_width = float(page.mediabox.width)
                page_height = float(page.mediabox.height)
                
                # 创建水印
                watermark_buffer = self.create_watermark_pdf(page_width, page_height)
                watermark_reader = PdfReader(watermark_buffer)
                watermark_page = watermark_reader.pages[0]
                
                # 合并水印到页面
                page.merge_page(watermark_page)
                writer.add_page(page)
                
                # 更新状态
                self.root.after(0, lambda p=page_num+1, t=total_pages: 
                    self.status_label.config(text=f"正在处理第 {p}/{t} 页..."))
            
            # 保存输出PDF
            with open(self.output_file.get(), 'wb') as output_file:
                writer.write(output_file)
            
            self.root.after(0, self.watermark_complete)
            
        except Exception as e:
            self.root.after(0, lambda: self.watermark_error(str(e)))
    
    def watermark_complete(self):
        """水印添加完成"""
        self.progress.stop()
        self.status_label.config(text="完成!")
        messagebox.showinfo("成功", f"水印添加完成!\n输出文件: {self.output_file.get()}")
    
    def watermark_error(self, error_msg):
        """水印添加出错"""
        self.progress.stop()
        self.status_label.config(text="出错")
        messagebox.showerror("错误", f"添加水印失败: {error_msg}")
    
    def add_watermark(self):
        """添加水印"""
        if not self.input_file.get():
            messagebox.showerror("错误", "请选择输入PDF文件")
            return
        
        if not self.output_file.get():
            messagebox.showerror("错误", "请指定输出PDF文件")
            return
        
        if not self.watermark_text.get().strip():
            messagebox.showerror("错误", "请输入水印文字")
            return
        
        # 在新线程中执行水印添加
        thread = threading.Thread(target=self.add_watermark_worker)
        thread.daemon = True
        thread.start()


def main():
    root = tk.Tk()
    app = PDFWatermarkTool(root)
    root.mainloop()


if __name__ == "__main__":
    main()
