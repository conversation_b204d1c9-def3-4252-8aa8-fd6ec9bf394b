# PDF水印添加工具

一个简单易用的Python桌面应用程序，用于给PDF文件添加自定义水印。

## 功能特点

- 🖥️ 图形化用户界面，操作简单
- 📄 支持批量处理PDF文件的每一页
- ✏️ 自定义水印文字（支持多行文本）
- 🎨 可调节字体大小、透明度和旋转角度
- 👀 水印预览功能
- 📊 实时进度显示

## 安装依赖

在运行程序之前，请先安装必要的Python库：

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install reportlab pypdf
```

## 使用方法

1. 运行程序：
   ```bash
   python pdf_watermark_tool.py
   ```

2. 选择输入PDF文件（点击"浏览"按钮）

3. 设置输出文件路径（会自动生成，也可以手动修改）

4. 配置水印设置：
   - **水印文字**：输入要显示的水印文本，支持多行（用回车分隔）
   - **字体大小**：调节水印文字的大小（20-100）
   - **透明度**：设置水印的透明度（0.1-1.0）
   - **旋转角度**：设置水印的倾斜角度（-90°到90°）

5. 点击"预览水印"查看水印效果信息

6. 点击"添加水印"开始处理PDF文件

## 水印效果

- 水印会以重复模式覆盖整个PDF页面
- 水印颜色为灰色，不会过度干扰原文档内容
- 支持多行水印文字，每行之间有适当间距
- 水印角度可调，默认45度倾斜效果

## 注意事项

- 确保输入的PDF文件没有被其他程序占用
- 输出文件路径必须是可写的
- 处理大文件时请耐心等待
- 建议在处理重要文档前先用测试文件验证效果

## 系统要求

- Python 3.6+
- Windows/macOS/Linux
- 足够的磁盘空间存储输出文件

## 故障排除

如果遇到问题：

1. 确认已正确安装所有依赖库
2. 检查PDF文件是否损坏或受保护
3. 确保有足够的磁盘空间
4. 检查文件路径是否包含特殊字符

## 许可证

本项目仅供学习和个人使用。
