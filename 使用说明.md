# PDF水印工具 - 使用说明

## 功能特点

### ✅ 已修复的问题
1. **中文字体支持** - 现在可以正确显示汉字
2. **字体大小调整** - 默认字体大小从50增加到60，范围扩展到30-120
3. **颜色优化** - 水印颜色从浅灰色(0.7)调整为深灰色(0.4)，提高可见性
4. **多水印支持** - 根据页面高度自动添加多个水印，确保长页面完全覆盖

### 🔧 技术改进

#### 中文字体支持
- 自动检测系统中文字体（Windows: 宋体、黑体、微软雅黑）
- 支持跨平台字体检测（Windows/macOS/Linux）
- 字体注册失败时自动回退到默认字体

#### 智能多水印布局
- **标准页面**: 根据内容自动判断水印数量
- **长页面**: 按页面高度的1/3间距分布水印
- **超长页面**: 确保整个页面都有水印覆盖

#### 水印分布算法
```
水印间距 = max(页面高度/3, 文字高度×2)
水印数量 = max(1, int(页面高度/水印间距) + 1)
```

## 使用方法

1. **启动程序**
   ```bash
   python pdf_watermark_tool.py
   ```

2. **选择输入PDF文件**
   - 点击"浏览"按钮选择要添加水印的PDF文件

3. **设置水印参数**
   - **水印文字**: 支持多行文字，用回车分隔
   - **字体大小**: 30-120范围，默认60
   - **透明度**: 0.1-1.0范围，默认0.3
   - **旋转角度**: 0-360度，默认45度

4. **预览水印效果**
   - 点击"预览水印"查看水印参数信息

5. **选择输出文件**
   - 点击"浏览"按钮选择保存位置

6. **添加水印**
   - 点击"添加水印"开始处理

## 测试文件

项目包含以下测试文件：
- `test_chinese.pdf` - 中文字体测试
- `long_test.pdf` - 长页面测试原文件
- `long_test_with_watermark.pdf` - 带多水印的长页面文件
- `test_watermark_*.pdf` - 不同页面尺寸的水印测试

## 技术细节

### 支持的页面类型
- ✅ 标准页面 (Letter: 612×792)
- ✅ 长页面 (2倍高度: 612×1584)  
- ✅ 超长页面 (3倍高度: 612×2376)
- ✅ 任意尺寸页面

### 水印特性
- 🎨 支持中文字符显示
- 📏 智能字体大小调整
- 🎯 自动居中对齐
- 🔄 可自定义旋转角度
- 👁️ 可调节透明度
- 📄 多行文字支持
- 🔢 智能多水印分布

## 依赖库
- `reportlab` - PDF生成和字体处理
- `pypdf` - PDF文件读写
- `tkinter` - GUI界面（Python内置）

安装依赖：
```bash
pip install reportlab pypdf
```
